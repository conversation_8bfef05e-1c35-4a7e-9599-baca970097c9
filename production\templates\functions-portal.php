<?php
/**
 * QuoteAI Portal WordPress Integration Functions
 * 
 * Add these functions to your theme's functions.php file or create a plugin.
 * These functions handle URL rewriting, admin settings, and portal functionality.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * QuoteAI Portal URL Rewriting
 * Handles URLs like /chat/tradie-name/
 */
function quoteai_portal_rewrite_rules() {
    // Add rewrite rule for chat portal
    add_rewrite_rule(
        '^chat/([^/]+)/?$',
        'index.php?pagename=chat&tradie_name=$matches[1]',
        'top'
    );
}
add_action('init', 'quoteai_portal_rewrite_rules');

/**
 * Add tradie_name query variable
 */
function quoteai_portal_query_vars($vars) {
    $vars[] = 'tradie_name';
    return $vars;
}
add_filter('query_vars', 'quoteai_portal_query_vars');

/**
 * Flush rewrite rules on theme activation
 */
function quoteai_portal_flush_rewrite_rules() {
    quoteai_portal_rewrite_rules();
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'quoteai_portal_flush_rewrite_rules');

/**
 * Create Chat page programmatically if it doesn't exist
 */
function quoteai_portal_create_chat_page() {
    $chat_page = get_page_by_path('chat');
    
    if (!$chat_page) {
        $page_data = array(
            'post_title'    => 'Chat Portal',
            'post_content'  => '<!-- QuoteAI Portal Page -->',
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_name'     => 'chat',
            'page_template' => 'page-chat.php'
        );
        
        $page_id = wp_insert_post($page_data);
        
        if ($page_id) {
            // Set the page template
            update_post_meta($page_id, '_wp_page_template', 'page-chat.php');
        }
    }
}
add_action('after_switch_theme', 'quoteai_portal_create_chat_page');

/**
 * Admin Settings for QuoteAI Portal
 */
function quoteai_portal_admin_menu() {
    add_options_page(
        'QuoteAI Portal Settings',
        'QuoteAI Portal',
        'manage_options',
        'quoteai-portal',
        'quoteai_portal_admin_page'
    );
}
add_action('admin_menu', 'quoteai_portal_admin_menu');

/**
 * Register settings
 */
function quoteai_portal_admin_init() {
    register_setting('quoteai_portal_settings', 'quoteai_api_url');
    register_setting('quoteai_portal_settings', 'quoteai_api_key');
    register_setting('quoteai_portal_settings', 'quoteai_portal_enabled');
    
    add_settings_section(
        'quoteai_portal_main',
        'QuoteAI Portal Configuration',
        'quoteai_portal_section_callback',
        'quoteai-portal'
    );
    
    add_settings_field(
        'quoteai_api_url',
        'API URL',
        'quoteai_api_url_callback',
        'quoteai-portal',
        'quoteai_portal_main'
    );
    
    add_settings_field(
        'quoteai_api_key',
        'API Key',
        'quoteai_api_key_callback',
        'quoteai-portal',
        'quoteai_portal_main'
    );
    
    add_settings_field(
        'quoteai_portal_enabled',
        'Enable Portal',
        'quoteai_portal_enabled_callback',
        'quoteai-portal',
        'quoteai_portal_main'
    );
}
add_action('admin_init', 'quoteai_portal_admin_init');

/**
 * Settings section callback
 */
function quoteai_portal_section_callback() {
    echo '<p>Configure your QuoteAI portal settings below:</p>';
}

/**
 * API URL field callback
 */
function quoteai_api_url_callback() {
    $value = get_option('quoteai_api_url', 'https://your-api-domain.com/api');
    echo '<input type="url" name="quoteai_api_url" value="' . esc_attr($value) . '" class="regular-text" />';
    echo '<p class="description">Enter your QuoteAI API URL</p>';
}

/**
 * API Key field callback
 */
function quoteai_api_key_callback() {
    $value = get_option('quoteai_api_key', '');
    echo '<input type="password" name="quoteai_api_key" value="' . esc_attr($value) . '" class="regular-text" />';
    echo '<p class="description">Enter your QuoteAI API key</p>';
}

/**
 * Portal enabled field callback
 */
function quoteai_portal_enabled_callback() {
    $value = get_option('quoteai_portal_enabled', '1');
    echo '<input type="checkbox" name="quoteai_portal_enabled" value="1" ' . checked(1, $value, false) . ' />';
    echo '<label for="quoteai_portal_enabled">Enable the QuoteAI portal</label>';
}

/**
 * Admin page HTML
 */
function quoteai_portal_admin_page() {
    ?>
    <div class="wrap">
        <h1>QuoteAI Portal Settings</h1>
        
        <div class="notice notice-info">
            <p><strong>Portal URL Format:</strong> Your portal will be accessible at URLs like <code><?php echo home_url('/chat/demo'); ?></code></p>
            <p><strong>Test URL:</strong> <a href="<?php echo home_url('/chat/demo'); ?>" target="_blank"><?php echo home_url('/chat/demo'); ?></a></p>
        </div>
        
        <form method="post" action="options.php">
            <?php
            settings_fields('quoteai_portal_settings');
            do_settings_sections('quoteai-portal');
            submit_button();
            ?>
        </form>
        
        <div class="card">
            <h2>Portal Status</h2>
            <table class="form-table">
                <tr>
                    <th>Chat Page</th>
                    <td>
                        <?php
                        $chat_page = get_page_by_path('chat');
                        if ($chat_page) {
                            echo '<span style="color: green;">✓ Created</span> - <a href="' . get_edit_post_link($chat_page->ID) . '">Edit Page</a>';
                        } else {
                            echo '<span style="color: red;">✗ Not Found</span>';
                        }
                        ?>
                    </td>
                </tr>
                <tr>
                    <th>Page Template</th>
                    <td>
                        <?php
                        $template_path = get_template_directory() . '/page-chat.php';
                        if (file_exists($template_path)) {
                            echo '<span style="color: green;">✓ Found</span>';
                        } else {
                            echo '<span style="color: red;">✗ Missing</span> - Upload page-chat.php to your theme directory';
                        }
                        ?>
                    </td>
                </tr>
                <tr>
                    <th>Portal Assets</th>
                    <td>
                        <?php
                        $js_path = get_template_directory() . '/quoteai-portal/assets/portal.js';
                        $css_path = get_template_directory() . '/quoteai-portal/assets/portal.css';
                        
                        if (file_exists($js_path) && file_exists($css_path)) {
                            echo '<span style="color: green;">✓ Found</span>';
                        } else {
                            echo '<span style="color: red;">✗ Missing</span> - Upload portal assets to your theme directory';
                        }
                        ?>
                    </td>
                </tr>
                <tr>
                    <th>URL Rewriting</th>
                    <td>
                        <?php
                        $rewrite_rules = get_option('rewrite_rules');
                        if (isset($rewrite_rules['^chat/([^/]+)/?$'])) {
                            echo '<span style="color: green;">✓ Active</span>';
                        } else {
                            echo '<span style="color: orange;">⚠ Inactive</span> - <a href="' . admin_url('options-permalink.php') . '">Flush Permalinks</a>';
                        }
                        ?>
                    </td>
                </tr>
            </table>
        </div>
        
        <div class="card">
            <h2>Troubleshooting</h2>
            <ul>
                <li><strong>Portal not loading:</strong> Check that all files are uploaded and permalinks are flushed</li>
                <li><strong>404 errors:</strong> Go to Settings → Permalinks and click "Save Changes"</li>
                <li><strong>JavaScript errors:</strong> Check browser console and verify API settings</li>
                <li><strong>Mobile issues:</strong> Test on actual devices, not just browser dev tools</li>
            </ul>
        </div>
    </div>
    <?php
}

/**
 * Shortcode for embedding portal in posts/pages
 */
function quoteai_portal_shortcode($atts) {
    $atts = shortcode_atts(array(
        'tradie' => 'demo',
        'height' => '600px',
        'width' => '100%'
    ), $atts);
    
    $portal_enabled = get_option('quoteai_portal_enabled', '1');
    if (!$portal_enabled) {
        return '<p>QuoteAI Portal is currently disabled.</p>';
    }
    
    $tradie_name = sanitize_text_field($atts['tradie']);
    $height = sanitize_text_field($atts['height']);
    $width = sanitize_text_field($atts['width']);
    
    return sprintf(
        '<iframe src="%s" width="%s" height="%s" frameborder="0" style="border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);"></iframe>',
        esc_url(home_url('/chat/' . $tradie_name)),
        esc_attr($width),
        esc_attr($height)
    );
}
add_shortcode('quoteai_portal', 'quoteai_portal_shortcode');

/**
 * Add portal-specific body classes
 */
function quoteai_portal_body_class($classes) {
    if (get_query_var('tradie_name')) {
        $classes[] = 'quoteai-portal-page';
        $classes[] = 'tradie-' . sanitize_html_class(get_query_var('tradie_name'));
    }
    return $classes;
}
add_filter('body_class', 'quoteai_portal_body_class');

/**
 * Disable WordPress admin bar on portal pages
 */
function quoteai_portal_disable_admin_bar() {
    if (get_query_var('tradie_name')) {
        show_admin_bar(false);
    }
}
add_action('wp', 'quoteai_portal_disable_admin_bar');

/**
 * Add portal-specific meta tags
 */
function quoteai_portal_meta_tags() {
    $tradie_name = get_query_var('tradie_name');
    if ($tradie_name) {
        $tradie_display_name = ucwords(str_replace('-', ' ', sanitize_text_field($tradie_name)));
        echo '<meta property="og:title" content="Chat with ' . esc_attr($tradie_display_name) . ' - ' . esc_attr(get_bloginfo('name')) . '">' . "\n";
        echo '<meta property="og:description" content="Get instant quotes through our AI-powered chat portal">' . "\n";
        echo '<meta property="og:type" content="website">' . "\n";
        echo '<meta property="og:url" content="' . esc_url(home_url('/chat/' . $tradie_name)) . '">' . "\n";
    }
}
add_action('wp_head', 'quoteai_portal_meta_tags');

/**
 * Security: Validate tradie names
 */
function quoteai_portal_validate_tradie_name($tradie_name) {
    // Only allow alphanumeric characters, hyphens, and underscores
    if (!preg_match('/^[a-zA-Z0-9\-_]+$/', $tradie_name)) {
        return false;
    }
    
    // Limit length
    if (strlen($tradie_name) > 50) {
        return false;
    }
    
    return true;
}

/**
 * Handle invalid tradie names
 */
function quoteai_portal_template_redirect() {
    $tradie_name = get_query_var('tradie_name');
    if ($tradie_name && !quoteai_portal_validate_tradie_name($tradie_name)) {
        wp_redirect(home_url());
        exit;
    }
}
add_action('template_redirect', 'quoteai_portal_template_redirect');
