/**
 * QuoteAI Portal - Standalone Production Bundle
 * This file contains all the necessary JavaScript for the portal to function
 * without requiring a build system or module bundler.
 */

(function() {
    'use strict';
    
    console.info('📦 QuoteAI Portal loading...');
    
    // Default configuration
    const DEFAULT_CONFIG = {
        apiUrl: 'https://0935-86-48-8-229.ngrok-free.app',
        apiKey: "';C\\}/{ap,76L'*m7U2!{K|21?n[*4i`;QuL",
        websiteUrl: 'getquoteai.com.au'
    };
    
    // Extract tradie name from URL
    function extractTradieNameFromUrl() {
        // WordPress URL format: /chat/tradie-name/
        const path = window.location.pathname;
        const pathMatch = path.match(/\/chat\/([^\/]+)/);
        if (pathMatch) {
            return decodeURIComponent(pathMatch[1]);
        }

        // Fallback to query parameter
        const urlParams = new URLSearchParams(window.location.search);
        const tradieParam = urlParams.get('tradie');
        if (tradieParam) {
            return decodeURIComponent(tradieParam);
        }

        return null;
    }
    
    // Mock Chat Widget Component (React-like structure)
    function createMockChatWidget(config) {
        const { tradieName } = config;
        
        // Mock messages for demo
        const mockMessages = [
            {
                type: 'bot',
                content: `Hello! Welcome to ${tradieName}'s chat portal. I'm here to help you get a quote for your project. What type of work do you need done?`,
                timestamp: Date.now() - 60000,
                suggestions: [
                    { text: 'Plumbing', value: 'I need plumbing work done' },
                    { text: 'Electrical', value: 'I need electrical work done' },
                    { text: 'General Repairs', value: 'I need general repairs done' },
                    { text: 'Roofing', value: 'I need roofing work done' }
                ]
            }
        ];
        
        let messages = [...mockMessages];
        let isSending = false;
        let selectedImages = [];
        
        // Create optimized portal HTML structure - Single header design
        function createPortalHTML() {
            return `
                <div class="portal-container">
                    <div class="portal-chat-area">
                        <div class="chat-container open portal-mode">
                            <div class="chat-header portal-mode">
                                <div class="chat-title">
                                    <h3>Chat with ${tradieName}</h3>
                                    <p>Online now</p>
                                </div>
                                <div class="chat-actions">
                                    <button class="icon-button" onclick="clearSession()" title="Clear chat" aria-label="Clear chat history">
                                        🗑️
                                    </button>
                                    <button class="icon-button" onclick="handleClose()" title="Close" aria-label="Close chat">
                                        ✕
                                    </button>
                                </div>
                            </div>

                            <div class="chat-messages portal-mode" id="chat-messages" role="log" aria-live="polite" aria-label="Chat messages">
                                <!-- Messages will be inserted here -->
                            </div>

                            <div class="image-preview-area" id="image-preview-area" style="display: none;" aria-label="Image preview area">
                                <!-- Image previews will be inserted here -->
                            </div>

                            <div class="chat-input portal-mode">
                                <div class="input-row">
                                    <textarea
                                        id="chat-textarea"
                                        class="portal-textarea"
                                        placeholder="Type your message here..."
                                        rows="1"
                                        style="resize: none; overflow: hidden;"
                                        aria-label="Type your message"
                                        autocomplete="off"
                                        spellcheck="true"
                                    ></textarea>
                                    <div class="portal-buttons">
                                        <button class="icon-button attach-button" onclick="triggerFileInput()" title="Attach Photo" aria-label="Attach photo">
                                            📎
                                        </button>
                                        <button class="icon-button camera-button" onclick="triggerCameraInput()" title="Take Photo" aria-label="Take photo">
                                            📷
                                        </button>
                                        <button class="icon-button send-button" onclick="sendMessage()" title="Send Message" aria-label="Send message">
                                            ➤
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hidden file input -->
                <input type="file" id="file-input" style="display: none;" accept="image/*" multiple onchange="handleFileSelect(event)" aria-label="File input">
            `;
        }
        
        // Render messages with smart scrolling
        function renderMessages(shouldScroll = true) {
            const messagesContainer = document.getElementById('chat-messages');
            if (!messagesContainer) return;

            // Store current scroll position and height
            const wasAtBottom = messagesContainer.scrollTop >= (messagesContainer.scrollHeight - messagesContainer.clientHeight - 50);
            const previousScrollHeight = messagesContainer.scrollHeight;

            messagesContainer.innerHTML = messages.map(message => {
                const timestamp = new Date(message.timestamp || Date.now()).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

                let attachmentsHTML = '';
                if (message.attachments && message.attachments.length > 0) {
                    attachmentsHTML = `
                        <div class="message-attachments">
                            ${message.attachments.map(url => `
                                <img src="${url}" alt="Attachment" class="message-image" onclick="openExpandedImage('${url}')" />
                            `).join('')}
                        </div>
                    `;
                }

                let suggestionsHTML = '';
                if (message.suggestions && message.suggestions.length > 0) {
                    suggestionsHTML = `
                        <div class="message-suggestions">
                            ${message.suggestions.map(suggestion => `
                                <button class="suggestion-button" onclick="handleSuggestionClick('${suggestion.value}')">
                                    ${suggestion.text}
                                </button>
                            `).join('')}
                        </div>
                    `;
                }

                return `
                    <div class="message ${message.type}">
                        <div class="message-content">
                            ${message.content ? `<div class="message-text">${message.content}</div>` : ''}
                            ${attachmentsHTML}
                            ${suggestionsHTML}
                        </div>
                        <div class="message-time">${timestamp}</div>
                    </div>
                `;
            }).join('');

            // Add typing indicator if sending
            if (isSending) {
                messagesContainer.innerHTML += `
                    <div class="message bot typing">
                        <div class="message-content">
                            <div class="typing-indicator">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                    </div>
                `;
            }

            // Smart scrolling: only scroll if user was at bottom or if explicitly requested
            if (shouldScroll && (wasAtBottom || messages.length <= 1)) {
                // Use requestAnimationFrame for smooth scrolling on mobile
                requestAnimationFrame(() => {
                    messagesContainer.scrollTo({
                        top: messagesContainer.scrollHeight,
                        behavior: 'smooth'
                    });
                });
            }
        }
        
        // Auto-expand textarea with mobile optimization
        function setupTextareaAutoExpand() {
            const textarea = document.getElementById('chat-textarea');
            if (!textarea) return;

            let isResizing = false;

            // Debounced resize function to prevent layout thrashing
            function resizeTextarea() {
                if (isResizing) return;
                isResizing = true;

                requestAnimationFrame(() => {
                    const currentHeight = textarea.style.height;
                    textarea.style.height = 'auto';
                    const newHeight = Math.min(textarea.scrollHeight, 120);

                    // Only update if height actually changed
                    if (currentHeight !== newHeight + 'px') {
                        textarea.style.height = newHeight + 'px';
                    }

                    isResizing = false;
                });
            }

            // Use input event with debouncing for mobile
            let inputTimeout;
            textarea.addEventListener('input', function() {
                clearTimeout(inputTimeout);
                inputTimeout = setTimeout(resizeTextarea, 16); // ~60fps
            });

            textarea.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Prevent zoom on focus for iOS
            textarea.addEventListener('focus', function() {
                if (/iPhone|iPad|iPod/.test(navigator.userAgent)) {
                    this.style.fontSize = '16px';
                }
            });
        }
        
        return {
            render: function() {
                const container = document.getElementById('portal-root');
                if (!container) {
                    console.error('❌ Portal root element not found');
                    return;
                }

                // Use requestAnimationFrame for smooth rendering
                requestAnimationFrame(() => {
                    container.innerHTML = createPortalHTML();

                    // Initialize components in sequence to prevent layout thrashing
                    requestAnimationFrame(() => {
                        renderMessages(true);
                        setupTextareaAutoExpand();
                        initializeConnectionStatus();

                        // Hide loading screen with smooth transition
                        requestAnimationFrame(() => {
                            document.body.classList.add('portal-loaded');
                            const loadingScreen = document.getElementById('loading-screen');
                            if (loadingScreen) {
                                setTimeout(() => {
                                    if (loadingScreen.parentNode) {
                                        loadingScreen.remove();
                                    }
                                }, 500);
                            }
                        });
                    });
                });
            },

            addMessage: function(message) {
                messages.push(message);
                renderMessages(true); // Always scroll for new messages
            },

            setLoading: function(loading) {
                isSending = loading;
                renderMessages(false); // Don't auto-scroll for loading state
            },

            clearMessages: function() {
                messages = [...mockMessages];
                renderMessages(true); // Scroll to show initial messages
            },

            updateConnectionStatus: function(isOnline) {
                const statusElement = document.querySelector('.chat-title p');
                if (statusElement) {
                    statusElement.textContent = isOnline ? 'Online now' : 'Connecting...';
                    statusElement.className = isOnline ? '' : 'offline';
                }
            }
        };

        // Initialize connection status monitoring
        function initializeConnectionStatus() {
            const statusElement = document.querySelector('.chat-title p');
            if (statusElement) {
                // Simulate connection monitoring
                let isOnline = navigator.onLine;
                statusElement.textContent = isOnline ? 'Online now' : 'Connecting...';
                statusElement.className = isOnline ? '' : 'offline';

                // Listen for online/offline events
                window.addEventListener('online', () => {
                    if (statusElement) {
                        statusElement.textContent = 'Online now';
                        statusElement.className = '';
                    }
                });

                window.addEventListener('offline', () => {
                    if (statusElement) {
                        statusElement.textContent = 'Connecting...';
                        statusElement.className = 'offline';
                    }
                });
            }
        }
    }
    
    // Global functions for HTML event handlers
    window.sendMessage = function() {
        const textarea = document.getElementById('chat-textarea');
        const text = textarea ? textarea.value.trim() : '';
        
        if (!text && selectedImages.length === 0) return;
        
        // Add user message
        const userMessage = {
            type: 'user',
            content: text,
            attachments: selectedImages.length > 0 ? selectedImages.map(img => img.url) : undefined,
            timestamp: Date.now()
        };
        
        chatWidget.addMessage(userMessage);
        
        // Clear input
        if (textarea) {
            textarea.value = '';
            textarea.style.height = 'auto';
        }
        
        // Clear images
        selectedImages = [];
        updateImagePreview();
        
        // Show loading
        chatWidget.setLoading(true);
        
        // Simulate bot response
        setTimeout(() => {
            const botMessage = {
                type: 'bot',
                content: `Thanks for your message! This is a demo response for the portal. In production, this would connect to the QuoteAI API.`,
                timestamp: Date.now(),
                suggestions: [
                    { text: 'Tell me more', value: 'Can you tell me more about this service?' },
                    { text: 'Get a quote', value: 'I\'d like to get a quote for this work' },
                    { text: 'Schedule consultation', value: 'Can we schedule a consultation?' }
                ]
            };
            
            chatWidget.addMessage(botMessage);
            chatWidget.setLoading(false);
        }, 1500);
    };
    
    window.handleSuggestionClick = function(value) {
        const textarea = document.getElementById('chat-textarea');
        if (textarea) {
            textarea.value = value;
            sendMessage();
        }
    };
    
    window.clearSession = function() {
        if (confirm('Are you sure you want to clear the chat history?')) {
            chatWidget.clearMessages();
        }
    };

    window.handleClose = function() {
        // Enhanced close handling with fallbacks
        if (window.history.length > 1) {
            window.history.back();
        } else if (document.referrer) {
            window.location.href = document.referrer;
        } else {
            // Fallback to home page
            window.location.href = '/';
        }
    };
    
    window.triggerFileInput = function() {
        const fileInput = document.getElementById('file-input');
        if (fileInput) {
            fileInput.removeAttribute('capture');
            fileInput.click();
        }
    };
    
    window.triggerCameraInput = function() {
        const fileInput = document.getElementById('file-input');
        if (fileInput) {
            fileInput.setAttribute('capture', 'environment');
            fileInput.click();
        }
    };
    
    let selectedImages = [];
    let isUpdatingPreview = false; // Prevent rapid updates

    window.handleFileSelect = function(event) {
        const files = Array.from(event.target.files || []);

        files.forEach(file => {
            if (file.type.startsWith('image/')) {
                const url = URL.createObjectURL(file);
                selectedImages.push({ file, url });
            }
        });

        updateImagePreview();
        updateTextareaPlaceholder();
    };

    function updateTextareaPlaceholder() {
        const textarea = document.getElementById('chat-textarea');
        if (textarea) {
            textarea.placeholder = selectedImages.length > 0
                ? 'Add a message (optional)...'
                : 'Type your message here...';
        }
    }

    function updateImagePreview() {
        if (isUpdatingPreview) return; // Prevent rapid updates
        isUpdatingPreview = true;

        // Use requestAnimationFrame to batch DOM updates
        requestAnimationFrame(() => {
            const previewArea = document.getElementById('image-preview-area');
            if (!previewArea) {
                isUpdatingPreview = false;
                return;
            }

            if (selectedImages.length === 0) {
                if (previewArea.style.display !== 'none') {
                    previewArea.style.display = 'none';
                }
                isUpdatingPreview = false;
                return;
            }

            // Only update if content actually changed
            const newContent = `
                <div class="image-preview-header">
                    <span>Selected Images (${selectedImages.length})</span>
                    <button onclick="clearAllImages()" class="clear-images-btn">Clear All</button>
                </div>
                <div class="image-preview-grid">
                    ${selectedImages.map((img, index) => `
                        <div class="image-preview-item">
                            <img src="${img.url}" alt="Preview" onclick="openExpandedImage('${img.url}')" />
                            <button onclick="removeImage(${index})" class="remove-image-btn">✕</button>
                        </div>
                    `).join('')}
                </div>
            `;

            if (previewArea.innerHTML !== newContent) {
                previewArea.style.display = 'block';
                previewArea.innerHTML = newContent;
            }

            isUpdatingPreview = false;
        });
    }
    
    window.removeImage = function(index) {
        if (selectedImages[index]) {
            URL.revokeObjectURL(selectedImages[index].url);
            selectedImages.splice(index, 1);
            updateImagePreview();
            updateTextareaPlaceholder();
        }
    };

    window.clearAllImages = function() {
        selectedImages.forEach(img => URL.revokeObjectURL(img.url));
        selectedImages = [];
        updateImagePreview();
        updateTextareaPlaceholder();
    };
    
    window.openExpandedImage = function(url) {
        // Create modal for expanded image view
        const modal = document.createElement('div');
        modal.className = 'expanded-image-modal';
        modal.innerHTML = `
            <div class="expanded-image-backdrop" onclick="closeExpandedImage()"></div>
            <div class="expanded-image-container">
                <img src="${url}" alt="Expanded view" class="expanded-image" />
                <button onclick="closeExpandedImage()" class="close-expanded-btn">✕</button>
            </div>
        `;
        document.body.appendChild(modal);
        
        window.closeExpandedImage = function() {
            modal.remove();
        };
    };
    
    // Initialize portal when DOM is ready
    let chatWidget;
    
    function initializePortal() {
        console.info('🎯 Initializing portal...');
        
        const tradieName = extractTradieNameFromUrl();
        if (!tradieName) {
            console.error('❌ No tradie name found in URL');
            return;
        }
        
        const config = {
            ...DEFAULT_CONFIG,
            ...(window.QUOTE_AI_CONFIG || {}),
            tradieName: tradieName
        };
        
        console.info('⚙️ Portal config:', config);
        
        chatWidget = createMockChatWidget(config);
        chatWidget.render();
        
        console.info('✅ Portal initialized successfully');
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializePortal);
    } else {
        initializePortal();
    }
    
})();
